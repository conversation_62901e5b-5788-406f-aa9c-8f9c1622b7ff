import os
import sys
import subprocess
import io
from google.cloud import speech

def convert_video_to_audio(video_path, audio_path):
    """Convert video to audio using ffmpeg"""
    try:
        # Try to use ffmpeg to extract audio
        cmd = [
            "ffmpeg", "-i", video_path, 
            "-vn", "-acodec", "pcm_s16le", 
            "-ar", "16000", "-ac", "1", 
            audio_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            return True
        else:
            print(f"ffmpeg error: {result.stderr}")
            return False
    except Exception as e:
        print(f"Error converting video to audio: {e}")
        return False

def transcribe_audio_google(audio_path):
    """Transcribe audio file using Google Cloud Speech-to-Text API"""
    try:
        # Initialize the client
        client = speech.SpeechClient()
        
        # Read the audio file
        with io.open(audio_path, "rb") as audio_file:
            content = audio_file.read()
        
        # Configure the audio and recognition settings
        audio = speech.RecognitionAudio(content=content)
        config = speech.RecognitionConfig(
            encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
            sample_rate_hertz=16000,
            language_code="zh-TW",  # Traditional Chinese
            enable_automatic_punctuation=True,
        )
        
        # Perform the transcription
        print("Transcribing audio with Google Cloud Speech-to-Text...")
        response = client.recognize(config=config, audio=audio)
        
        # Extract the transcript
        transcript = ""
        for result in response.results:
            transcript += result.alternatives[0].transcript
        
        return transcript
    except Exception as e:
        print(f"Error transcribing audio with Google Cloud: {e}")
        return None

def main():
    video_path = r"C:\Temp\AJ\JA Live All-Employee Session 1.mov"
    audio_path = r"C:\Temp\AJ\meeting_audio.wav"
    transcript_path = r"C:\Temp\AJ\meeting_transcript.txt"
    
    # Check if video file exists
    if not os.path.exists(video_path):
        print(f"Video file not found: {video_path}")
        return
    
    print("Converting video to audio...")
    if not convert_video_to_audio(video_path, audio_path):
        print("Failed to convert video to audio. Please ensure ffmpeg is installed.")
        return
    
    print("Transcribing audio...")
    transcript = transcribe_audio_google(audio_path)
    
    if transcript:
        with open(transcript_path, "w", encoding="utf-8") as f:
            f.write(transcript)
        print(f"Transcript saved to {transcript_path}")
        print("First 500 characters of transcript:")
        print(transcript[:500])
    else:
        print("Failed to transcribe audio.")
    
    # Clean up temporary audio file
    if os.path.exists(audio_path):
        os.remove(audio_path)

if __name__ == "__main__":
    main()